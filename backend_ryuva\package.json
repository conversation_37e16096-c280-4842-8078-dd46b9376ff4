{"name": "backend_ryuva", "version": "1.0.0", "main": "server.js", "scripts": {"start": "nodemon server.js", "dev": "nodemon server.js", "prod": "node server.js"}, "type": "module", "author": "Ryuva Team", "license": "ISC", "description": "Ryuva E-commerce Backend API", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "stripe": "^14.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}